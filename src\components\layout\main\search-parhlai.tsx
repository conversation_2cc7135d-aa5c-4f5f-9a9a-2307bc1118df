import { Input } from "@/components/ui/input";
import useMediaQuery from "@/hooks/use-media-query";
import { Search } from "react-feather";

const SearchParhlai = () => {
	const isDesktop = useMediaQuery("(min-width: 1024px)");
	if (isDesktop)
		return (
			<div className="flex max-w-96 flex-1 gap-x-3">
				<Input
					startIcon={Search}
					placeholder="Search Here"
					className="sans h-14 rounded-[10px] placeholder:text-gray-400 text-gray-600 items-center"
				/>
			</div>
		);
	else
		return (
			<div className="lg:hidden">
				<Search size={24} />
			</div>
		);
};

export default SearchParhlai;
